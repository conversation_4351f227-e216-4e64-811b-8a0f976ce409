# 快速开始
## 1. 安装
### 安装 dots.ocr
```shell
conda create -n dots_ocr python=3.12
conda activate dots_ocr

git clone https://github.com/rednote-hilab/dots.ocr.git
cd dots.ocr

# 安装 pytorch，请根据你的 cuda 版本查看 https://pytorch.org/get-started/previous-versions/
pip install torch==2.7.0 torchvision==0.22.0 torchaudio==2.7.0 --index-url https://download.pytorch.org/whl/cu128
pip install -e .
```

如果安装遇到问题，可以尝试使用我们的 [Docker 镜像](https://hub.docker.com/r/rednotehilab/dots.ocr) 来简化安装，按照以下步骤操作：
```shell
git clone https://github.com/rednote-hilab/dots.ocr.git
cd dots.ocr
pip install -e .
```


### 下载模型权重
> 💡**注意：** 请使用不包含句点的目录名（例如，使用 `DotsOCR` 而不是 `dots.ocr`）作为模型保存路径。这是在我们与 Transformers 集成之前的临时解决方案。
```shell
python3 tools/download_model.py

# 使用 modelscope
python3 tools/download_model.py --type modelscope
```


## 2. 部署
### vLLM 推理
我们强烈推荐使用 vllm 进行部署和推理。我们所有的评估结果都基于 vllm 版本 0.9.1。
[Docker 镜像](https://hub.docker.com/r/rednotehilab/dots.ocr) 基于官方 vllm 镜像。你也可以按照 [Dockerfile](https://github.com/rednote-hilab/dots.ocr/blob/master/docker/Dockerfile) 自己构建部署环境。

```shell
# 首先需要将模型注册到 vllm
python3 tools/download_model.py
export hf_model_path=./weights/DotsOCR  # 下载的模型权重路径，请使用不包含句点的目录名（例如，使用 `DotsOCR` 而不是 `dots.ocr`）作为模型保存路径。这是在我们与 Transformers 集成之前的临时解决方案。
export PYTHONPATH=$(dirname "$hf_model_path"):$PYTHONPATH
sed -i '/^from vllm\.entrypoints\.cli\.main import main$/a\
from DotsOCR import modeling_dots_ocr_vllm' `which vllm`  # 如果你自己下载了模型权重，请将 `DotsOCR` 替换为你的模型保存目录名，记住使用不包含句点的目录名（例如，使用 `DotsOCR` 而不是 `dots.ocr`）

# 启动 vllm 服务器
CUDA_VISIBLE_DEVICES=0 vllm serve ${hf_model_path} --tensor-parallel-size 1 --gpu-memory-utilization 0.95  --chat-template-content-format string --served-model-name model --trust-remote-code

# 如果遇到 ModuleNotFoundError: No module named 'DotsOCR' 错误，请检查上面关于保存模型目录名的注意事项。

# vllm api 演示
python3 ./demo/demo_vllm.py --prompt_mode prompt_layout_all_en
```

### Hugginface 推理
```shell
python3 demo/demo_hf.py
```

<details>
<summary><b>Hugginface 推理详细信息</b></summary>

```python
import torch
from transformers import AutoModelForCausalLM, AutoProcessor, AutoTokenizer
from qwen_vl_utils import process_vision_info
from dots_ocr.utils import dict_promptmode_to_prompt

model_path = "./weights/DotsOCR"
model = AutoModelForCausalLM.from_pretrained(
    model_path,
    attn_implementation="flash_attention_2",
    torch_dtype=torch.bfloat16,
    device_map="auto",
    trust_remote_code=True
)
processor = AutoProcessor.from_pretrained(model_path, trust_remote_code=True)

image_path = "demo/demo_image1.jpg"
prompt = """请从PDF图像中输出布局信息，包括每个布局元素的边界框、类别以及边界框内对应的文本内容。

1. 边界框格式：[x1, y1, x2, y2]

2. 布局类别：可能的类别包括 ['Caption', 'Footnote', 'Formula', 'List-item', 'Page-footer', 'Page-header', 'Picture', 'Section-header', 'Table', 'Text', 'Title']。

3. 文本提取和格式化规则：
    - Picture：对于 'Picture' 类别，应省略文本字段。
    - Formula：将其文本格式化为 LaTeX。
    - Table：将其文本格式化为 HTML。
    - 其他所有类别（Text、Title 等）：将其文本格式化为 Markdown。

4. 约束条件：
    - 输出文本必须是图像中的原始文本，不得翻译。
    - 所有布局元素必须按照人类阅读顺序排序。

5. 最终输出：整个输出必须是单个 JSON 对象。
"""

messages = [
        {
            "role": "user",
            "content": [
                {
                    "type": "image",
                    "image": image_path
                },
                {"type": "text", "text": prompt}
            ]
        }
    ]

# 推理准备
text = processor.apply_chat_template(
    messages,
    tokenize=False,
    add_generation_prompt=True
)
image_inputs, video_inputs = process_vision_info(messages)
inputs = processor(
    text=[text],
    images=image_inputs,
    videos=video_inputs,
    padding=True,
    return_tensors="pt",
)

inputs = inputs.to("cuda")

# 推理：生成输出
generated_ids = model.generate(**inputs, max_new_tokens=24000)
generated_ids_trimmed = [
    out_ids[len(in_ids) :] for in_ids, out_ids in zip(inputs.input_ids, generated_ids)
]
output_text = processor.batch_decode(
    generated_ids_trimmed, skip_special_tokens=True, clean_up_tokenization_spaces=False
)
print(output_text)

```

</details>

## 3. 文档解析
**基于 vLLM 服务器**，你可以使用以下命令解析图像或 PDF 文件：
```bash

# 解析所有布局信息，包括检测和识别
# 解析单个图像
python3 dots_ocr/parser.py demo/demo_image1.jpg
# 解析单个 PDF
python3 dots_ocr/parser.py demo/demo_pdf1.pdf  --num_thread 64  # 对于页数较多的 PDF，可以尝试更大的 num_threads

# 仅布局检测
python3 dots_ocr/parser.py demo/demo_image1.jpg --prompt prompt_layout_only_en

# 仅解析文本，除了页眉和页脚
python3 dots_ocr/parser.py demo/demo_image1.jpg --prompt prompt_ocr

# 根据边界框解析布局信息
python3 dots_ocr/parser.py demo/demo_image1.jpg --prompt prompt_grounding_ocr --bbox 163 241 1536 705

```

<details>
<summary><b>输出结果</b></summary>

1.  **结构化布局数据** (`demo_image1.json`)：包含检测到的布局元素的 JSON 文件，包括它们的边界框、类别和提取的文本。
2.  **处理后的 Markdown 文件** (`demo_image1.md`)：从所有检测到的单元格的连接文本生成的 Markdown 文件。
    *   还提供了一个额外版本 `demo_image1_nohf.md`，它排除了页眉和页脚，以便与 Omnidocbench 和 olmOCR-bench 等基准测试兼容。
3.  **布局可视化** (`demo_image1.jpg`)：在原始图像上绘制检测到的布局边界框。

</details>

## 4. 演示
你可以使用以下命令运行演示，或直接在 [在线演示](https://dotsocr.xiaohongshu.com/) 中尝试
```bash
python demo/demo_gradio.py
```

我们还提供了定位 OCR 的演示：
```bash
python demo/demo_gradio_annotion.py
```


### 公式文档示例